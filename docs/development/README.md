# 🛠️ UniScribe 开发文档

这个目录包含了 UniScribe 项目的开发相关文档，包括开发指南、最佳实践、架构说明等。

## 📋 文档列表

### 🌍 国际化

#### [国际化指南](i18n_guide.md)

- **创建日期**: 项目初期
- **适用场景**: 多语言支持开发
- **状态**: ✅ 活跃维护
- **描述**: 详细的多语言支持实现和维护指南

## 🎯 开发指南

### 🚀 快速开始

#### 环境设置

```bash
# 克隆项目
git clone https://github.com/your-org/uniscribe-web.git
cd uniscribe-web

# 安装依赖
npm install

# 复制环境变量模板
cp .env.example .env
# 编辑 .env 文件，填入真实的配置值

# 启动开发服务器
npm run dev
```

#### 开发工具

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **编辑器**: VS Code (推荐)
- **浏览器**: Chrome/Firefox (用于调试)

### 📁 项目结构

```
uniscribe-web/
├── docs/                    # 📚 项目文档
│   ├── security/           # 🔒 安全文档
│   └── development/        # 🛠️ 开发文档
├── src/                    # 💻 源代码
│   ├── app/               # 📱 Next.js App Router
│   ├── components/        # 🧩 React 组件
│   ├── config/           # ⚙️ 配置文件
│   ├── hooks/            # 🎣 自定义 Hooks
│   ├── lib/              # 📚 工具库
│   ├── messages/         # 🌍 国际化消息
│   └── stores/           # 🗄️ 状态管理
├── public/               # 📁 静态资源
└── security-check.js     # 🔒 安全检查脚本
```

### 🔧 开发工作流

#### 1. 功能开发

```bash
# 创建功能分支
git checkout -b feature/your-feature-name

# 开发功能
npm run dev

# 运行测试
npm run test

# 检查代码质量
npm run lint
```

#### 2. 安全检查

```bash
# 运行安全检查
node security-check.js

# 检查依赖漏洞
npm audit

# 修复可自动修复的漏洞
npm audit fix
```

#### 3. 构建和部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器（本地测试）
npm run start
```

## 📖 编码规范

### 🎨 代码风格

- 使用 ESLint 和 Prettier 保持代码一致性
- 遵循 React 和 Next.js 最佳实践
- 使用 TypeScript（逐步迁移）

### 📝 命名规范

- **组件**: PascalCase (`UserProfile.js`)
- **文件**: camelCase (`userUtils.js`)
- **常量**: UPPER_SNAKE_CASE (`API_ENDPOINTS`)
- **变量**: camelCase (`userName`)

### 🗂️ 文件组织

- 按功能模块组织组件
- 相关文件放在同一目录
- 使用 index.js 作为模块入口

### 💬 注释规范

```javascript
/**
 * 用户认证相关的工具函数
 * @param {string} token - JWT token
 * @returns {boolean} 是否有效
 */
function validateToken(token) {
  // 实现逻辑...
}
```

## 🧩 组件开发

### 📦 组件结构

```javascript
// components/UserProfile/UserProfile.js
import { useState } from "react";
import styles from "./UserProfile.module.css";

export default function UserProfile({ user }) {
  const [isEditing, setIsEditing] = useState(false);

  return <div className={styles.container}>{/* 组件内容 */}</div>;
}
```

### 🎣 自定义 Hooks

```javascript
// hooks/useUserProfile.js
import { useState, useEffect } from "react";

export function useUserProfile(userId) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 获取用户数据的逻辑
  }, [userId]);

  return { user, loading };
}
```

## 🌍 国际化开发

### 添加新语言

1. 在 `src/messages/` 下创建语言目录
2. 复制英文消息文件作为模板
3. 翻译所有消息
4. 在 `src/config/i18n.js` 中添加语言配置

### 使用翻译

```javascript
import { useTranslations } from "next-intl";

export default function MyComponent() {
  const t = useTranslations("common");

  return <h1>{t("welcome")}</h1>;
}
```

详细信息请参考 [国际化指南](i18n_guide.md)。

## 🔒 安全开发

### 安全检查清单

- [ ] 运行 `node security-check.js`
- [ ] 检查 CSP 配置是否正确
- [ ] 验证用户输入
- [ ] 使用 HTTPS
- [ ] 保护敏感信息

### 常见安全问题

- **XSS**: 使用 React 的内置保护，避免 `dangerouslySetInnerHTML`
- **CSRF**: 使用 Next.js 的内置保护
- **敏感信息**: 不要在客户端代码中暴露 API 密钥

## 🧪 测试

### 单元测试

```bash
# 运行所有测试
npm run test

# 运行特定测试
npm run test -- UserProfile.test.js

# 监听模式
npm run test:watch
```

### 集成测试

```bash
# 运行 E2E 测试
npm run test:e2e
```

## 📊 性能优化

### 代码分割

- 使用 Next.js 的动态导入
- 按路由分割代码
- 懒加载非关键组件

### 图片优化

- 使用 Next.js Image 组件
- 提供多种格式（WebP, AVIF）
- 实现响应式图片

## 🔗 有用的资源

### 官方文档

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev/)
- [Tailwind CSS](https://tailwindcss.com/docs)

### 项目特定资源

- [安全配置文件](../../src/config/security.js)
- [国际化配置](../../src/config/i18n.js)
- [API 端点配置](../../src/constants/endpoints.js)

## 📞 开发支持

### 获取帮助

- 查看相关文档
- 搜索已知问题
- 联系团队成员

### 贡献代码

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

---

**目录维护者**: 开发团队  
**最后更新**: 2025年5月29日  
**下次审查**: 2025年8月29日
