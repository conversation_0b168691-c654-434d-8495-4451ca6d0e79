# 📚 UniScribe 项目文档

欢迎来到 UniScribe 项目文档中心！这里包含了项目的所有技术文档和指南。

## 📁 文档结构

### 🔒 安全文档 (`security/`)

包含所有安全相关的配置、修复和指南：

- **[安全审计报告](security/SECURITY_AUDIT_REPORT.md)** - 完整的安全审计结果和部署指南
- **[安全改进记录](security/SECURITY_IMPROVEMENTS.md)** - 所有安全漏洞修复和配置优化记录
- **[CSP 故障排除指南](security/CSP_TROUBLESHOOTING_GUIDE.md)** - Content Security Policy 问题的解决方案

### 🛠️ 开发文档 (`development/`)

包含开发相关的指南和最佳实践：

- **[国际化指南](development/i18n_guide.md)** - 多语言支持的实现和维护指南

## 🚀 快速开始

### 安全检查

在部署前，请运行安全检查脚本：

```bash
node security-check.js
```

### 开发环境设置

```bash
npm install
npm run dev
```

## 📋 文档维护

### 添加新文档

1. 根据文档类型选择合适的目录
2. 使用清晰的文件名（英文，用连字符分隔）
3. 在相应的 README.md 中添加链接
4. 确保文档包含必要的元信息（创建日期、作者等）

### 文档分类指南

#### 安全文档 (`security/`)

- 安全漏洞修复记录
- 安全配置指南
- 渗透测试报告
- 安全最佳实践

#### 开发文档 (`development/`)

- 开发环境设置
- 代码规范
- API 文档
- 架构设计文档

#### 部署文档 (`deployment/`) - 待创建

- 部署流程
- 环境配置
- 监控设置
- 故障排除

#### 用户文档 (`user/`) - 待创建

- 用户手册
- 功能说明
- 常见问题解答

## 🔍 文档搜索

### 按主题查找

- **安全**: 查看 `security/` 目录
- **开发**: 查看 `development/` 目录
- **配置**: 搜索包含 "config" 的文件
- **故障排除**: 搜索包含 "troubleshooting" 或 "guide" 的文件

### 按文件类型查找

- **修复记录**: `*_FIX_*.md`
- **指南**: `*_GUIDE.md`
- **报告**: `*_REPORT.md`
- **配置**: `*_CONFIG*.md`

## 📝 文档规范

### 文件命名

- 使用英文
- 单词间用下划线分隔
- 包含文档类型后缀（如 `_GUIDE.md`, `_REPORT.md`）
- 示例：`SECURITY_AUDIT_REPORT.md`

### 文档格式

- 使用 Markdown 格式
- 包含清晰的标题层级
- 添加适当的 emoji 增强可读性
- 包含目录（对于长文档）
- 添加创建/更新日期

### 内容要求

- 简洁明了的描述
- 详细的步骤说明
- 代码示例（如适用）
- 相关链接和参考资料
- 联系信息（如需要）

## 🔗 相关链接

- [项目主 README](../README.md)
- [安全配置文件](../src/config/security.js)
- [安全检查脚本](../security-check.js)

## 📞 联系方式

如有文档相关问题，请联系：

- 邮箱：<EMAIL>
- 项目维护者：Augment Agent

---

**最后更新**: 2025年5月29日
**文档版本**: 1.0
**维护状态**: ✅ 活跃维护
