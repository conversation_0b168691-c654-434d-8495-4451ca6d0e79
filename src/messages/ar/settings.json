{"page": {"title": "الإعدادات", "subtitle": "إدارة إعدادات حسابك وتفضيلاتك", "backButton": "عودة"}, "navigation": {"profile": "الملف الشخصي", "preferences": "تفضيلات", "notifications": "الإشعارات", "dangerZone": "منطقة الخطر", "apiKeyManagement": "مفاتيح API"}, "profile": {"title": "الملف الشخصي", "description": "إدارة معلوماتك الشخصية", "firstName": "الاسم الأول", "lastName": "اسم العائلة", "emailAddress": "عنوان البريد الإلكتروني", "save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "جاري الحفظ...", "saved": "تم الحفظ!", "successMessage": "تم حفظ الملف الشخصي بنجاح", "errorMessage": "فشل في حفظ الملف الشخصي. يرجى المحاولة مرة أخرى."}, "preferences": {"title": "تفضيلات", "description": "إدارة تفضيلات التطبيق الخاصة بك", "language": {"label": "لغة الواجهة", "description": "اختر لغتك المفضلة لواجهة التطبيق"}, "timezone": {"label": "منطقة زمنية", "description": "اختر منطقتك الزمنية", "placeholder": "اختر المنطقة الزمنية...", "searchPlaceholder": "البحث عن المنطقة الزمنية...", "loading": "جارٍ تحميل المناطق الزمنية...", "successMessage": "تم تحديث المنطقة الزمنية بنجاح", "errorMessage": "فشل في تحديث المنطقة الزمنية. يرجى المحاولة مرة أخرى."}}, "notifications": {"title": "إشعارات البريد الإلكتروني", "description": "إدارة تفضيلات إشعارات البريد الإلكتروني الخاصة بك", "transcriptionSuccess": {"label": "إشعارات نجاح النسخ", "description": "تلقَّ إشعارات عند اكتمال نسخ الصوت الخاص بك"}, "quotaReset": {"label": "إشعارات إعادة تعيين حصة النسخ", "description": "استلم إشعارات عندما يتم إعادة تعيين حصة النسخ الخاصة بك"}, "productUpdates": {"label": "تحديثات المنتج والميزات الجديدة", "description": "استلم إشعارات حول تحديثات المنتج والميزات الجديدة"}, "successMessage": "تم تحديث إعدادات الإشعارات", "errorMessage": "فشل في تحديث إعدادات الإشعارات. يرجى المحاولة مرة أخرى."}, "dangerZone": {"title": "منطقة الخطر", "description": "إجراءات لا يمكن التراجع عنها", "deleteAccount": {"label": "<PERSON><PERSON><PERSON> الح<PERSON>اب", "description": "احذف حسابك وجميع البيانات بشكل دائم", "button": "<PERSON><PERSON><PERSON> الح<PERSON>اب", "dialog": {"title": "<PERSON><PERSON><PERSON> الح<PERSON>اب", "description": "هل أنت متأكد أنك تريد حذف حسابك؟", "warning": "لا يمكن التراجع عن هذا الإجراء.", "consequences": {"title": "عند حذف حسابك:", "items": ["سيتم حذف جميع بياناتك بشكل دائم", "ستفقد الوصول إلى جميع نسخك النصية", "سيتم إلغاء اشتراكك (إذا كان ذلك قابلاً للتطبيق)", "لا يمكن استعادة حسابك", "لن تتمكن بريدك الإلكتروني من إنشاء حساب جديد بعد الآن."]}, "confirmation": {"label": "لتأكيد، اكتب DELETE أدناه:", "placeholder": "اكتب DELETE هنا"}, "buttons": {"cancel": "إلغاء", "delete": "<PERSON><PERSON><PERSON>", "deleting": "حذف..."}, "successMessage": "تم حذف حسابك", "errorMessage": "فشل في حذف الحساب. يرجى المحاولة مرة أخرى."}}}, "apiKeyManagement": {"title": "إدارة مفتاح واجهة برمجة التطبيقات", "description": "إدارة مفاتيح واجهة برمجة التطبيقات الخاصة بك للوصول إلى UniScribe API", "requiresSubscription": "يتطلب الوصول إلى واجهة برمجة التطبيقات اشتراكًا نشطًا أو خطة LTD", "upgradePrompt": "قم بترقية خطتك للوصول إلى ميزات إدارة واجهة برمجة التطبيقات", "createKey": "إنشاء مفتاح جديد", "createFirstKey": "إنشاء مفتاح API الأول الخاص بك", "noKeys": "لا توجد مفاتيح API", "noKeysDescription": "لم تقم بإنشاء أي مفاتيح API بعد.", "maxKeysReached": "تم الوصول إلى الحد الأقصى لعدد مفاتيح واجهة برمجة التطبيقات (5 مفاتيح)", "maxKeysDescription": "احذف مف<PERSON><PERSON><PERSON> API الحالي قبل إنشاء مفتاح جديد", "keyName": "اسم المفتاح", "keyNamePlaceholder": "أدخل اسمًا لمفتاح API الخاص بك", "expiration": "انتهاء الصلاحية", "noExpiration": "لا انتهاء صلاحية", "days": "أيام", "createdAt": "تم الإنشاء", "lastUsed": "آخر استخدام", "neverUsed": "لم يتم استخدامه مطلقًا", "actions": "إجراءات", "rename": "إعادة تسمية", "reset": "إعادة تعيين", "delete": "<PERSON><PERSON><PERSON>", "copy": "نسخ", "copied": "تم النسخ!", "active": "نشط", "expired": "منتهي الصلاحية", "keyPreview": "معاينة المفتاح", "fullKey": "مف<PERSON><PERSON><PERSON> API الكامل", "keyWarning": "هذه هي المرة الوحيدة التي سترى فيها مفتاح API الكامل. قم بتخزينه بأمان.", "keyStorageWarning": "إذا فقدت هذه المفتاح، ستحتاج إلى إعادة تعيينه للحصول على مفتاح جديد.", "confirmDelete": "هل أنت متأكد أنك تريد حذف مفتاح API هذا؟", "confirmDeleteDescription": "لا يمكن التراجع عن هذا الإجراء. ستتوقف جميع التطبيقات التي تستخدم هذه المفتاح عن العمل على الفور.", "confirmReset": "هل أنت متأكد أنك تريد إعادة تعيين مفتاح API هذا؟", "confirmResetDescription": "سيتم إنشاء قيمة مفتاح جديدة. سيتوقف المفتاح القديم عن العمل على الفور.", "createDialog": {"title": "إنشاء مفتاح API", "nameLabel": "اسم المفتاح", "namePlaceholder": "مفتاح واجهة برمجة التطبيقات للإنتاج", "expirationLabel": "انتهاء الصلاحية (اختياري)", "expirationPlaceholder": "<PERSON><PERSON><PERSON> الأيام", "expirationHelp": "اتركه فارغًا لعدم انتهاء الصلاحية (1-3650 يومًا)", "cancel": "إلغاء", "create": "إنشاء مفتاح", "creating": "إنشاء..."}, "renameDialog": {"title": "إعادة تسمية مفتاح API", "nameLabel": "اسم جديد", "cancel": "إلغاء", "save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "جارٍ الحفظ...", "description": "قم بتغيير اسم مفتاح واجهة برمجة التطبيقات الخاص بك."}, "keyCreatedDialog": {"title": "تم إنشاء مفتاح API بنجاح", "copyButton": "نسخ مفتاح واجهة برمجة التطبيقات", "close": "إغلاق"}, "keyResetDialog": {"title": "تم إعادة تعيين مفتاح API بنجاح", "copyButton": "نسخ مفتاح API جديد", "close": "إغلاق"}, "successMessages": {"keyCreated": "تم إنشاء مفتاح API بنجاح", "keyUpdated": "تم تحديث مفتاح API بنجاح", "keyReset": "تم إعادة تعيين مفتاح API بنجاح", "keyDeleted": "تم حذف مفتاح API بنجاح"}, "errorMessages": {"loadFailed": "فشل في تحميل مفاتيح API. يرجى المحاولة مرة أخرى.", "createFailed": "فشل في إنشاء مفتاح API. يرجى المحاولة مرة أخرى.", "updateFailed": "فشل في تحديث مفتاح API. يرجى المحاولة مرة أخرى.", "resetFailed": "فشل في إعادة تعيين مفتاح API. يرجى المحاولة مرة أخرى.", "deleteFailed": "فشل في حذف مفتاح API. يرجى المحاولة مرة أخرى.", "accessDenied": "يتطلب الوصول إلى واجهة برمجة التطبيقات اشتراكًا نشطًا أو خطة LTD", "maxKeysReached": "تم الوصول إلى الحد الأقصى لعدد مفاتيح API (5)", "invalidName": "يجب أن يكون اسم مفتاح API بين 1 و 100 حرف", "invalidExpiration": "يجب أن تكون مدة الانتهاء بين 1 و 3650 يومًا", "keyNotFound": "لم يتم العثور على مفتاح API", "nameExists": "اسم مفتاح API موجود بالفعل"}, "upgradePlan": "خطة الترقية", "viewApiDocs": "عرض وثائق واجهة برمجة التطبيقات"}}