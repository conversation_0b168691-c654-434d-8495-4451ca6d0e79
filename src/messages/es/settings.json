{"page": {"title": "Configuraciones", "subtitle": "Administra la configuración y preferencias de tu cuenta", "backButton": "Atrás"}, "navigation": {"profile": "Perfil", "preferences": "Preferencias", "notifications": "Notificaciones", "dangerZone": "Zona de Peligro", "apiKeyManagement": "Claves API"}, "profile": {"title": "Perfil", "description": "Gestiona tu información personal", "firstName": "Nombre de pila", "lastName": "Apellido", "emailAddress": "Dirección de correo electrónico", "save": "Guardar", "saving": "Guardando...", "saved": "¡Guardado!", "successMessage": "Perfil guardado con éxito", "errorMessage": "Error al guardar el perfil. Por favor, inténtelo de nuevo."}, "preferences": {"title": "Preferencias", "description": "Gestiona las preferencias de tu aplicación", "language": {"label": "Idioma de la interfaz", "description": "Elija su idioma preferido para la interfaz de la aplicación."}, "timezone": {"label": "Zona Horaria", "description": "Seleccione su zona horaria", "placeholder": "Seleccionar zona horaria...", "searchPlaceholder": "Buscar zona horaria...", "loading": "Cargando zonas horarias...", "successMessage": "Zona horaria actualizada con éxito", "errorMessage": "Error al actualizar la zona horaria. Por favor, inténtelo de nuevo."}}, "notifications": {"title": "Notificaciones por correo electrónico", "description": "Gestiona tus preferencias de notificación por correo electrónico", "transcriptionSuccess": {"label": "Notificaciones de Éxito de Transcripción", "description": "Reciba notificaciones cuando su transcripción de audio esté completa."}, "quotaReset": {"label": "Notificaciones de Restablecimiento de Cuota de Transcripción", "description": "Reciba notificaciones cuando se restablezca su cuota de transcripción."}, "productUpdates": {"label": "Actualizaciones del producto y nuevas funciones", "description": "Reciba notificaciones sobre actualizaciones de productos y nuevas funciones."}, "successMessage": "Configuración de notificaciones actualizada", "errorMessage": "Error al actualizar la configuración de notificaciones. Por favor, inténtelo de nuevo."}, "dangerZone": {"title": "Zona de Peligro", "description": "Acciones que no se pueden deshacer", "deleteAccount": {"label": "Eliminar cuenta", "description": "Eliminar permanentemente su cuenta y todos los datos", "button": "Eliminar Cuenta", "dialog": {"title": "Eliminar cuenta", "description": "¿Está seguro de que desea eliminar su cuenta?", "warning": "Esta acción no se puede deshacer.", "consequences": {"title": "<PERSON>uando eliminas tu cuenta:", "items": ["Todos sus datos serán eliminados permanentemente.", "Perderás el acceso a todas tus transcripciones.", "Su suscripción será cancelada (si corresponde)", "Su cuenta no puede ser recuperada.", "Su correo electrónico ya no podrá crear una nueva cuenta."]}, "confirmation": {"label": "Para confirmar, escriba DELETE a continuación:", "placeholder": "Escriba DELETE aquí"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "deleting": "Eliminando..."}, "successMessage": "Su cuenta ha sido eliminada.", "errorMessage": "No se pudo eliminar la cuenta. Por favor, inténtelo de nuevo."}}}, "apiKeyManagement": {"title": "Gestión de Claves API", "description": "Administra tus claves API para acceder a la API de UniScribe", "requiresSubscription": "El acceso a la API requiere una suscripción activa o un plan LTD.", "upgradePrompt": "Actualice su plan para acceder a las funciones de gestión de API.", "createKey": "<PERSON><PERSON><PERSON> Clave", "createFirstKey": "Crea tu primera clave API", "noKeys": "No hay claves API", "noKeysDescription": "Aún no has creado ninguna clave de API.", "maxKeysReached": "Se ha alcanzado el límite máximo de claves API (5 claves)", "maxKeysDescription": "Elimine una clave API existente antes de crear una nueva.", "keyName": "Nombre de la clave", "keyNamePlaceholder": "Ingrese un nombre para su clave API", "expiration": "Expiración", "noExpiration": "Sin expiración", "days": "días", "createdAt": "<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON>ltimo utiliza<PERSON>", "neverUsed": "Nunca utilizado", "actions": "Acciones", "rename": "Renombrar", "reset": "Restablecer", "delete": "Eliminar", "copy": "Copiar", "copied": "¡Copiado!", "active": "Activo", "expired": "<PERSON><PERSON><PERSON>", "keyPreview": "Vista previa de la clave", "fullKey": "Clave API completa", "keyWarning": "Esta es la única vez que verás la clave API completa. Almacénala de forma segura.", "keyStorageWarning": "Si pierde esta clave, necesitará restablecerla para obtener una nueva.", "confirmDelete": "¿Está seguro de que desea eliminar esta clave API?", "confirmDeleteDescription": "Esta acción no se puede deshacer. Todas las aplicaciones que utilizan esta clave dejarán de funcionar inmediatamente.", "confirmReset": "¿Está seguro de que desea restablecer esta clave API?", "confirmResetDescription": "Esto generará un nuevo valor de clave. La clave antigua dejará de funcionar inmediatamente.", "createDialog": {"title": "Crear clave API", "nameLabel": "Nombre de la clave", "namePlaceholder": "Clave de API de Producción", "expirationLabel": "Expiración (opcional)", "expirationPlaceholder": "Número de días", "expirationHelp": "Dejar vacío para no tener expiración (1-3650 días)", "cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> clave", "creating": "Creando..."}, "renameDialog": {"title": "Renombrar la clave API", "nameLabel": "Nuevo Nombre", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "saving": "Guardando...", "description": "Cambia el nombre de tu clave API."}, "keyCreatedDialog": {"title": "Clave API creada con éxito", "copyButton": "Copiar clave API", "close": "<PERSON><PERSON><PERSON>"}, "keyResetDialog": {"title": "Restablecimiento de la clave API exitoso", "copyButton": "Copiar nueva clave API", "close": "<PERSON><PERSON><PERSON>"}, "successMessages": {"keyCreated": "Clave API creada con éxito", "keyUpdated": "Clave API actualizada con éxito", "keyReset": "La clave de API se restableció con éxito.", "keyDeleted": "Clave API eliminada con éxito"}, "errorMessages": {"loadFailed": "Error al cargar las claves de API. Por favor, inténtelo de nuevo.", "createFailed": "No se pudo crear la clave de API. Por favor, inténtelo de nuevo.", "updateFailed": "Error al actualizar la clave de API. Por favor, inténtelo de nuevo.", "resetFailed": "No se pudo restablecer la clave de API. Por favor, inténtelo de nuevo.", "deleteFailed": "No se pudo eliminar la clave de API. Por favor, inténtelo de nuevo.", "accessDenied": "El acceso a la API requiere una suscripción activa o un plan LTD.", "maxKeysReached": "Se ha alcanzado el número máximo de claves API (5)", "invalidName": "El nombre de la clave API debe tener entre 1 y 100 caracteres.", "invalidExpiration": "La expiración debe estar entre 1 y 3650 días.", "keyNotFound": "Clave de API no encontrada", "nameExists": "El nombre de la clave API ya existe."}, "upgradePlan": "Plan de Actualización", "viewApiDocs": "Ver documentación de la API"}}