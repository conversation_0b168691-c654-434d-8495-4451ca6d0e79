export const createFileProcessor = (file, options = {}) => {
  // 检查文件大小是否超过 WebAssembly 的 2GB 限制
  const MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024; // 2GB in bytes
  if (file.size > MAX_FILE_SIZE) {
    console.warn(
      `File size (${(file.size / (1024 * 1024)).toFixed(2)} MB) exceeds the 2GB WebAssembly limit. Skipping media processing.`
    );
    return null; // 对于超过2GB的文件，直接跳过处理
  }

  const isVideo = file.type.startsWith("video/");
  const isWav = file.type === "audio/wav";
  const isM4a = file.type === "audio/mp4" || file.type === "audio/x-m4a";
  const isLargeWav = isWav && file.size > 5 * 1024 * 1024;

  // 添加选项跳过大小检查
  const shouldConvertWav = options.skipSizeCheck ? isWav : isLargeWav;
  const shouldConvertAudio = shouldConvertWav || isM4a;

  if (isVideo) {
    return {
      command: [
        "-i",
        "input_file",
        "-vn",
        "-acodec",
        "libmp3lame",
        "-q:a",
        "2",
        "output.mp3",
      ],
      outputFilename: "output.mp3",
      outputType: "audio/mp3",
    };
  }

  if (shouldConvertAudio) {
    return {
      command: [
        "-i",
        "input_file",
        "-codec:a",
        "libmp3lame",
        "-q:a",
        "2",
        "converted.mp3",
      ],
      outputFilename: "converted.mp3",
      outputType: "audio/mp3",
    };
  }

  return null; // No processing needed
};
