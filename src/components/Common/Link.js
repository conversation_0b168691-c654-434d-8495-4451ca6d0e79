import NextLink from "next/link";
import { Link as IntlLink } from "@/i18n/navigation";

// 定义不需要语言前缀的路径
const excludedPaths = ["/api"];

export function Link({ href, ...props }) {
  // 如果没有提供 href，返回普通的 span
  if (!href) {
    const { children, className, ...restProps } = props;
    return (
      <span className={className} {...restProps}>
        {children}
      </span>
    );
  }

  try {
    // 如果是排除的路径，使用 NextLink
    const isExcludedPath = excludedPaths.some((path) => href.startsWith(path));

    if (isExcludedPath) {
      return <NextLink href={href} {...props} />;
    }

    return <IntlLink href={href} {...props} />;
  } catch (error) {
    // 如果出错，回退到普通的 a 标签
    console.error("Error rendering Link component:", error);
    const { children, className, ...restProps } = props;
    return (
      <a href={href} className={className} {...restProps}>
        {children}
      </a>
    );
  }
}
