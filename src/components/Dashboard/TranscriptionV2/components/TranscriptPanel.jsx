"use client";

import { useRef, useEffect, useState, useCallback } from "react";
import {
  Play,
  Pause,
  Edit,
  CheckCircle,
  X,
  Search,
  Replace,
  Copy,
  Loader2,
} from "lucide-react";
import SpeakerAvatar from "@/components/ui/speaker-avatar";
import { formatDuration } from "@/lib/utils";
import TaskStatusHandler from "@/components/Dashboard/TaskStatusHandler";
import { useTranslations } from "next-intl";
import { TASK_STATUS } from "@/constants/task";
import { FILE_STATUS } from "@/constants/file";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useAuthStore } from "@/stores/useAuthStore";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import { transcriptionService } from "@/services/api/transcriptionService";
import { trackEvent, trackAnonymousEvent } from "@/lib/analytics";
import { ToastContainer } from "@/components/ui/toast";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { SearchReplaceDialog } from "./SearchReplaceDialog";
import GuestModeDialog from "@/components/Dashboard/GuestModeDialog";

// 移除了匿名用户预览时间限制相关的常量

export function TranscriptPanel({
  segments,
  currentSegment,
  onSegmentClick,
  onBatchSegmentsUpdate,
  taskStatuses,
  taskErrors,
  taskIds,
  onRetry,
  isTaskRetrying,
  getTaskRetryInfo,
  playerRef,
  isAnonymous,
  insufficientMinutes = 0,
  totalDuration = 0,
  fileId,
  sourceType,
  sourceUrl, // Kept for future use if needed to display source URL
  isEditingSegment,
  setIsEditingSegment,
  isAligned = true, // 新增 isAligned 字段，默认为 true（向后兼容）
  fileStatus, // 新增文件状态参数
  fileSize, // 新增文件大小参数
  canPlay = true, // 新增 canPlay 参数，默认为 true
}) {
  const t = useTranslations("transcription");
  const containerRef = useRef(null);
  const segmentRefs = useRef({});
  const { user } = useAuthStore();
  const { summary } = useEntitlementsStore();
  const { openDialog } = useUpgradeDialogStore();

  // 编辑模式状态
  const [editingSegmentId, setEditingSegmentId] = useState(null);
  const [editedText, setEditedText] = useState("");
  const textareaRefs = useRef({});

  // 移动端检测，需在所有用到 isMobile 的地方之前
  const isMobile = useMediaQuery("(max-width: 768px)");

  // 跟踪播放状态
  const [isPlaying, setIsPlaying] = useState(false);

  // 跟踪鼠标悬停的 segment
  const [hoveredSegmentId, setHoveredSegmentId] = useState(null);

  // Speaker 名称编辑相关状态
  const [editingSpeakerId, setEditingSpeakerId] = useState(null);
  const [editedSpeakerName, setEditedSpeakerName] = useState("");
  const [hoveredSpeakerId, setHoveredSpeakerId] = useState(null);
  const [isSavingSpeaker, setIsSavingSpeaker] = useState(false);

  // 同步编辑状态到父组件 - 包括文本编辑和speaker编辑
  useEffect(() => {
    setIsEditingSegment(editingSegmentId !== null || editingSpeakerId !== null);
  }, [editingSegmentId, editingSpeakerId, setIsEditingSegment]);

  // Toast 状态
  const [toast, setToast] = useState(null);

  // 搜索相关状态
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [searchDialogTab, setSearchDialogTab] = useState("search");
  const [searchValue, setSearchValue] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);
  const [highlightedSegments, setHighlightedSegments] = useState(new Set());

  // 替换相关状态
  const [replaceValue, setReplaceValue] = useState("");
  const [isReplacing, setIsReplacing] = useState(false);
  const [isReplacingAll, setIsReplacingAll] = useState(false);

  // 保存相关状态
  const [isSaving, setIsSaving] = useState(false);

  // 复制相关状态
  const [isCopying, setIsCopying] = useState(false);

  // 访客弹框状态
  const [showGuestModeDialog, setShowGuestModeDialog] = useState(false);
  const [guestDialogSource, setGuestDialogSource] = useState("");

  // 本地segments副本，用于实时更新替换后的文本
  const [localSegments, setLocalSegments] = useState(segments || []);

  // 同步外部segments变化 - 智能合并编辑状态
  useEffect(() => {
    if (!segments) {
      setLocalSegments([]);
      return;
    }

    // 如果没有正在编辑的内容，直接同步
    if (editingSpeakerId === null && editingSegmentId === null) {
      setLocalSegments(segments);
      return;
    }

    // 如果有正在编辑的内容，需要智能合并
    setLocalSegments((prevSegments) => {
      // 创建一个Map来快速查找新的segments
      const newSegmentMap = new Map(segments.map((seg) => [seg.id, seg]));

      return prevSegments.map((prevSeg) => {
        // 如果这个segment正在被编辑，保留本地状态
        if (
          prevSeg.id === editingSpeakerId ||
          prevSeg.id === editingSegmentId
        ) {
          return prevSeg;
        }

        // 否则使用新的数据
        return newSegmentMap.get(prevSeg.id) || prevSeg;
      });
    });
  }, [segments, editingSpeakerId, editingSegmentId]);

  // 处理Toast自动消失
  useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => {
        setToast(null);
      }, toast.duration || 2000);

      return () => clearTimeout(timer);
    }
  }, [toast]);

  // 监听播放器状态变化
  useEffect(() => {
    const checkPlayingStatus = () => {
      if (playerRef.current) {
        setIsPlaying(playerRef.current.isPlaying());
      }
    };

    // 每秒检查一次播放状态
    const interval = setInterval(checkPlayingStatus, 1000);

    return () => clearInterval(interval);
  }, [playerRef]);

  const getMaxViewableTime = () => {
    // 匿名用户现在可以查看所有内容，不再有时间限制
    if (insufficientMinutes > 0) {
      return totalDuration - insufficientMinutes * 60;
    }
    return Infinity;
  };

  const maxViewableTime = getMaxViewableTime();

  const isSegmentViewable = (segment) => segment.end <= maxViewableTime;

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingSegmentId(null);
    setEditedText("");
  }, []);

  // 用于防止重复保存的标志
  const isSavingRef = useRef(false);

  // 保存编辑的文本
  const handleSaveEdit = useCallback(
    async (segment) => {
      // 如果已经在保存中，直接返回
      if (isSavingRef.current) {
        return;
      }

      // 如果文本没有变化，直接退出编辑模式，不发送请求
      if (editedText === segment.text) {
        setEditingSegmentId(null);
        return;
      }

      // 设置保存中标志和loading状态
      isSavingRef.current = true;
      setIsSaving(true);

      try {
        const response = await transcriptionService.batchUpdateSegments(
          fileId,
          [{ id: segment.id, text: editedText }]
        );

        if (response.data && response.data.success) {
          // 更新本地段落数据
          // 新的批量接口返回的是segments数组，取第一个元素
          const updatedSegment = response.data.segments[0];

          // 同时更新本地segments副本，确保数据一致性
          setLocalSegments((prevSegments) =>
            prevSegments.map((seg) =>
              seg.id === segment.id ? updatedSegment : seg
            )
          );

          // 通知父组件更新段落数据
          if (onSegmentClick && updatedSegment) {
            onSegmentClick(updatedSegment);
          }

          // 记录保存成功事件
          if (isAnonymous) {
            trackAnonymousEvent("transcript_edit_save", {
              success: true,
            });
          } else {
            trackEvent("transcript_edit_save", {
              success: true,
            });
          }

          // 显示保存成功提示
          setToast({
            message: t("transcript.saveSuccess"),
            variant: "success",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("Error updating segment:", error);

        // 记录保存失败事件
        if (isAnonymous) {
          trackAnonymousEvent("transcript_edit_save", {
            success: false,
            error: error.message || "Unknown error",
          });
        } else {
          trackEvent("transcript_edit_save", {
            success: false,
            error: error.message || "Unknown error",
          });
        }

        // 显示保存失败提示
        setToast({
          message: t("transcript.saveError"),
          variant: "error",
          duration: 3000,
        });
      } finally {
        // 重置保存中标志和loading状态
        isSavingRef.current = false;
        setIsSaving(false);

        // 退出编辑模式
        setEditingSegmentId(null);
      }
    },
    [editedText, fileId, onSegmentClick, t, isAnonymous]
  );

  // 处理文本变化
  const handleTextChange = useCallback((e) => {
    setEditedText(e.target.value);

    // 自动调整 textarea 高度
    const textarea = e.target;
    textarea.style.height = "auto";
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, []);

  // 检查说话人识别是否正在进行中
  // 新逻辑：说话人识别任务完成但还未对齐时，仍然认为是处理中状态
  const isSpeakerDiarizationProcessing = useCallback(() => {
    const speakerDiarizationStatus = taskStatuses?.speakerDiarization;

    // 如果说话人识别任务还在处理中，返回 true
    if (speakerDiarizationStatus === TASK_STATUS.PROCESSING) {
      return true;
    }

    // 如果说话人识别任务已完成，但还未对齐，也返回 true
    if (speakerDiarizationStatus === TASK_STATUS.COMPLETED && !isAligned) {
      return true;
    }

    // 其他情况返回 false（包括任务失败、未开始，或已完成且已对齐）
    return false;
  }, [taskStatuses, isAligned]);

  // 处理 speaker 名称编辑
  const handleSpeakerEditClick = useCallback(
    (e, segment) => {
      if (isMobile) return; // 移动端禁用编辑
      if (isSpeakerDiarizationProcessing()) return; // 说话人识别进行中时禁用编辑
      e.stopPropagation(); // 阻止事件冒泡

      setEditingSpeakerId(segment.id);
      setEditedSpeakerName(segment.speaker || "Speaker");
    },
    [isMobile, isSpeakerDiarizationProcessing]
  );

  // 取消 speaker 名称编辑
  const handleSpeakerEditCancel = useCallback(() => {
    setEditingSpeakerId(null);
    setEditedSpeakerName("");
  }, []);

  // 处理 speaker 名称变化
  const handleSpeakerNameChange = useCallback((e) => {
    setEditedSpeakerName(e.target.value);
  }, []);

  // 保存 speaker 名称 - single 模式（只修改当前段落）
  const handleSpeakerSaveSingle = useCallback(
    async (segment) => {
      if (!editedSpeakerName.trim()) {
        // 如果名称为空，不保存
        handleSpeakerEditCancel();
        return;
      }

      // 如果正在保存中，防止重复提交
      if (isSavingSpeaker) {
        return;
      }

      setIsSavingSpeaker(true);

      try {
        // 调用后端接口更新 speaker
        const response = await transcriptionService.batchUpdateSpeakers(
          fileId,
          [{ id: segment.id, speaker: editedSpeakerName.trim() }]
        );

        if (response.data && response.data.success) {
          // 更新本地 segments 数据
          const updatedSegments = response.data.segments;
          setLocalSegments((prevSegments) => {
            const segmentMap = new Map(
              updatedSegments.map((seg) => [seg.id, seg])
            );
            return prevSegments.map((seg) => segmentMap.get(seg.id) || seg);
          });

          // 通知父组件更新数据 - 使用更新后的segment
          const updatedSegment = updatedSegments.find(
            (seg) => seg.id === segment.id
          );
          if (onSegmentClick && updatedSegment) {
            onSegmentClick(updatedSegment);
          }

          // 显示成功提示
          setToast({
            message: t("transcript.speakerUpdateSuccess"),
            variant: "success",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("Error updating speaker:", error);

        // 显示错误提示
        setToast({
          message: t("transcript.speakerUpdateError"),
          variant: "error",
          duration: 3000,
        });
      } finally {
        setIsSavingSpeaker(false);
        // 退出编辑模式
        handleSpeakerEditCancel();
      }
    },
    [editedSpeakerName, handleSpeakerEditCancel, fileId, isSavingSpeaker, t]
  );

  // 保存 speaker 名称 - global 模式（修改所有相同名称的段落）
  const handleSpeakerSaveGlobal = useCallback(
    async (segment) => {
      if (!editedSpeakerName.trim()) {
        // 如果名称为空，不保存
        handleSpeakerEditCancel();
        return;
      }

      // 如果正在保存中，防止重复提交
      if (isSavingSpeaker) {
        return;
      }

      setIsSavingSpeaker(true);

      const originalSpeakerName = segment.speaker || "Speaker";
      const newSpeakerName = editedSpeakerName.trim();

      try {
        // 找到所有相同 speaker 名称的段落
        const segmentsToUpdate = localSegments
          .filter((seg) => (seg.speaker || "Speaker") === originalSpeakerName)
          .map((seg) => ({ id: seg.id, speaker: newSpeakerName }));

        // 调用后端接口更新所有相同 speaker 的段落
        const response = await transcriptionService.batchUpdateSpeakers(
          fileId,
          segmentsToUpdate
        );

        if (response.data && response.data.success) {
          // 更新本地 segments 数据
          const updatedSegments = response.data.segments;
          setLocalSegments((prevSegments) => {
            const segmentMap = new Map(
              updatedSegments.map((seg) => [seg.id, seg])
            );
            return prevSegments.map((seg) => segmentMap.get(seg.id) || seg);
          });

          // 对于全局更新，使用批量更新函数来更新父组件的transcription状态
          if (onBatchSegmentsUpdate) {
            onBatchSegmentsUpdate(updatedSegments);
          } else if (onSegmentClick) {
            // 如果没有批量更新函数，回退到逐个更新（向后兼容）
            updatedSegments.forEach((updatedSeg) => {
              onSegmentClick(updatedSeg);
            });
          }

          // 显示成功提示
          setToast({
            message: t("transcript.speakerUpdateGlobalSuccess"),
            variant: "success",
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("Error updating speakers globally:", error);

        // 显示错误提示
        setToast({
          message: t("transcript.speakerUpdateError"),
          variant: "error",
          duration: 3000,
        });
      } finally {
        setIsSavingSpeaker(false);
        // 退出编辑模式
        handleSpeakerEditCancel();
      }
    },
    [
      editedSpeakerName,
      handleSpeakerEditCancel,
      fileId,
      localSegments,
      isSavingSpeaker,
      t,
      onBatchSegmentsUpdate,
      onSegmentClick,
    ]
  );

  // 处理单击事件 - 选择段落并跳转到对应时间点，但保持当前播放/暂停状态
  const handleSegmentClick = useCallback(
    (segment, skipSeek = false) => {
      if (!isSegmentViewable(segment)) {
        return;
      }

      // 如果当前正在编辑，不做任何操作
      if (editingSegmentId !== null) {
        return;
      }

      // 检查是否有任何文本输入元素处于活动状态（编辑模式）
      const isEditingText =
        document.activeElement.tagName === "TEXTAREA" ||
        document.activeElement.tagName === "INPUT" ||
        document.activeElement.isContentEditable;

      // 如果用户正在编辑文本，不做任何操作
      if (isEditingText) {
        return;
      }

      // 判断是否是当前选中的 segment
      const isCurrentSegmentClick = currentSegment?.id === segment.id;

      // 选择段落 - 只有在不是当前选中的 segment 时才更新
      if (!isCurrentSegmentClick) {
        onSegmentClick(segment);
      }

      // 跳转到对应时间点，但保持当前播放/暂停状态
      // 以下情况不跳转：
      // 1. skipSeek 为 true（用于编辑模式）
      // 2. 点击的是当前选中的 segment（无论是否正在播放）
      if (playerRef.current && !skipSeek && !isCurrentSegmentClick) {
        playerRef.current.seekTo(segment.start);
        // 不调用 play() 或 pause()，保持当前状态
      }
    },
    [
      isSegmentViewable,
      editingSegmentId,
      onSegmentClick,
      playerRef,
      currentSegment,
    ]
  );

  // 处理播放按钮点击
  const handlePlayClick = useCallback(
    (e, segment) => {
      e.stopPropagation(); // 阻止事件冒泡

      if (!isSegmentViewable(segment)) {
        return;
      }

      onSegmentClick(segment);
      if (playerRef.current) {
        // 如果点击的是当前正在播放的段落，则切换播放/暂停状态
        if (currentSegment?.id === segment.id && isPlaying) {
          playerRef.current.pause();
        } else {
          // 否则，跳转到该段落并开始播放
          playerRef.current.seekTo(segment.start);
          playerRef.current.play();
        }
      }
    },
    [isSegmentViewable, onSegmentClick, playerRef, currentSegment, isPlaying]
  );

  // 处理编辑按钮点击
  const handleEditClick = useCallback(
    (e, segment, source = "button") => {
      if (isMobile) return; // 移动端禁用编辑入口
      e.stopPropagation(); // 阻止事件冒泡

      if (!isSegmentViewable(segment)) {
        return;
      }

      // 打点记录编辑按钮点击事件
      if (isAnonymous) {
        trackAnonymousEvent("transcript_edit_clicked", {
          source: source,
          segment_id: segment.id,
        });
      } else {
        trackEvent("transcript_edit_clicked", {
          source: source,
          segment_id: segment.id,
        });
      }

      // 如果不是当前选中的段落，先选中它并跳转到对应时间点
      if (currentSegment?.id !== segment.id) {
        onSegmentClick(segment); // 更新当前选中的 segment

        // 跳转到对应时间点
        if (playerRef.current) {
          playerRef.current.seekTo(segment.start);
        }
      }

      setEditingSegmentId(segment.id);
      setEditedText(segment.text);

      // 进入编辑状态时，关闭查找替换弹框
      if (showSearchDialog) {
        setShowSearchDialog(false);
        // 清除搜索状态和高亮
        setSearchValue("");
        setSearchResults([]);
        setHighlightedSegments(new Set());
        setCurrentSearchIndex(0);
        setReplaceValue("");
      }

      // 使用 setTimeout 确保 DOM 已更新
      setTimeout(() => {
        if (textareaRefs.current[segment.id]) {
          const textarea = textareaRefs.current[segment.id];
          textarea.focus();

          // 调整 textarea 高度以匹配内容
          textarea.style.height = "auto";
          textarea.style.height = `${textarea.scrollHeight}px`;
        }
      }, 0);
    },
    [
      isSegmentViewable,
      currentSegment,
      onSegmentClick,
      playerRef,
      fileId,
      isMobile,
      isAnonymous,
    ]
  );

  useEffect(() => {
    if (!currentSegment || !segmentRefs.current[currentSegment.id]) {
      return;
    }

    segmentRefs.current[currentSegment.id].scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }, [currentSegment]);

  // 处理按下 Escape 键取消编辑和 Cmd/Ctrl+S 保存
  useEffect(() => {
    const handleKeyDown = (e) => {
      // 处理 Escape 键取消编辑 - 优先处理 speaker 编辑
      if (e.key === "Escape") {
        if (editingSpeakerId !== null) {
          handleSpeakerEditCancel();
        } else if (editingSegmentId !== null) {
          handleCancelEdit();
        }
      }

      // 处理 Cmd/Ctrl+S 保存
      if (
        (e.ctrlKey || e.metaKey) &&
        e.key === "s" &&
        editingSegmentId !== null
      ) {
        e.preventDefault(); // 阻止浏览器默认的保存行为

        // 找到当前编辑的 segment
        const editingSegment = localSegments?.find(
          (seg) => seg.id === editingSegmentId
        );
        if (editingSegment) {
          handleSaveEdit(editingSegment);
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [
    editingSegmentId,
    editingSpeakerId,
    handleCancelEdit,
    handleSaveEdit,
    handleSpeakerEditCancel,
    localSegments,
  ]);

  // 处理点击文档其他区域时保存编辑
  useEffect(() => {
    if (editingSegmentId === null) return;

    const handleClickOutside = (e) => {
      // 检查点击是否在编辑区域外
      const isOutsideClick =
        textareaRefs.current[editingSegmentId] &&
        !textareaRefs.current[editingSegmentId].contains(e.target) &&
        !e.target.closest("button"); // 排除按钮点击

      if (isOutsideClick) {
        // 找到当前编辑的 segment
        const editingSegment = localSegments?.find(
          (seg) => seg.id === editingSegmentId
        );
        if (editingSegment) {
          // 使用 setTimeout 确保在其他事件处理完成后执行保存
          setTimeout(() => {
            handleSaveEdit(editingSegment);
          }, 100);
        }
      }
    };

    // 立即添加事件监听器
    document.addEventListener("mousedown", handleClickOutside);

    // 添加一个额外的 mouseup 事件监听器，以防 mousedown 事件被阻止
    document.addEventListener("mouseup", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("mouseup", handleClickOutside);
    };
  }, [editingSegmentId, localSegments, handleSaveEdit]);

  // 处理点击文档其他区域时取消 speaker 编辑
  useEffect(() => {
    if (editingSpeakerId === null) return;

    const handleClickOutside = (e) => {
      // 检查点击是否在 speaker 编辑区域外
      const speakerEditContainer = e.target.closest(
        "[data-speaker-edit-container]"
      );
      const isClickOnButton = e.target.closest("button");
      const isClickOnInput = e.target.tagName === "INPUT";

      // 如果点击的不是编辑容器内的元素，且不是按钮或输入框，则取消编辑
      if (!speakerEditContainer && !isClickOnButton && !isClickOnInput) {
        handleSpeakerEditCancel();
      }
    };

    // 使用 setTimeout 确保在组件渲染完成后添加事件监听器
    const timer = setTimeout(() => {
      document.addEventListener("mousedown", handleClickOutside);
    }, 0);

    return () => {
      clearTimeout(timer);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [editingSpeakerId, handleSpeakerEditCancel]);

  const getOverlayProps = () => {
    // 移除匿名用户的遮罩逻辑，匿名用户现在可以查看完整内容
    if (insufficientMinutes > 0) {
      // Check if user has enough remaining minutes to unlock
      const remainingMinutes = summary?.remainingCredits || 0;
      if (remainingMinutes > insufficientMinutes) {
        return {
          message: t("transcript.insufficient_minutes", {
            minutes: insufficientMinutes,
          }),
          buttonText: t("common.unlock"),
          buttonAction: async () => {
            try {
              await transcriptionService.unlockTranscription(fileId);
              // Refresh the page or update the state after successful unlock
              window.location.reload();
            } catch (error) {
              console.error("Error unlocking transcription:", error);
              // 处理错误情况
              // 如果是余额不足，显示升级对话框
              if (error.data?.code === 30006) {
                // 显示升级对话框
                openDialog({
                  source: "transcript_insufficient_minutes",
                  defaultPlanType: "yearly",
                });
              } else {
                // 其他错误，可以使用浏览器的 alert 或者其他方式显示错误信息
                alert(
                  t("errors.unlock_failed", {
                    defaultValue:
                      "Failed to unlock transcription. Please try again later.",
                  })
                );
              }
            }
          },
        };
      }

      // Default upgrade case when user doesn't have enough remaining minutes
      return {
        message: t("transcript.insufficient_minutes", {
          minutes: insufficientMinutes,
        }),
        buttonText: t("common.upgrade"),
        buttonAction: () =>
          openDialog({
            source: "transcript_insufficient_minutes",
            defaultPlanType: "yearly",
          }),
      };
    }
    return null;
  };

  const overlayProps = getOverlayProps();
  // 匿名用户不再显示遮罩，只有余额不足时才显示
  const shouldShowOverlay = insufficientMinutes > 0;

  // 搜索功能相关函数
  const performSearch = useCallback(
    (searchTerm) => {
      if (!searchTerm.trim() || !localSegments.length) {
        setSearchResults([]);
        setHighlightedSegments(new Set());
        setCurrentSearchIndex(0);
        return;
      }

      const results = [];
      const highlightedSegmentIds = new Set();

      localSegments.forEach((segment, segmentIndex) => {
        const text = segment.text.toLowerCase();
        const searchTermLower = searchTerm.toLowerCase();

        let startIndex = 0;
        let segmentMatchIndex = 0;

        while (true) {
          // 在文本中查找关键词
          const foundIndex = text.indexOf(searchTermLower, startIndex);
          if (foundIndex === -1) break;

          // 修改为部分匹配：所有找到的匹配项都有效
          const isValidMatch = true;

          if (isValidMatch) {
            // 获取原始文本中的匹配部分（保持原始大小写）
            const originalMatch = segment.text.slice(
              foundIndex,
              foundIndex + searchTermLower.length
            );

            results.push({
              segmentId: segment.id,
              segmentIndex: segmentIndex,
              matchIndex: foundIndex,
              matchText: originalMatch,
              segment: segment,
              uniqueId: `${segment.id}-${segmentMatchIndex}`,
              segmentMatchIndex: segmentMatchIndex,
            });
            highlightedSegmentIds.add(segment.id);
            segmentMatchIndex++;
          }

          // 移动到下一个位置继续搜索
          startIndex = foundIndex + 1;
        }
      });

      setSearchResults(results);
      setHighlightedSegments(highlightedSegmentIds);
      setCurrentSearchIndex(0);

      // 如果有搜索结果，滚动到第一个匹配的段落
      if (results.length > 0) {
        scrollToSearchResult(0, results);
      }
    },
    [localSegments]
  );

  // 滚动到搜索结果，不使用useCallback避免依赖问题
  const scrollToSearchResult = (index, results = null) => {
    const resultsToUse = results || searchResults;
    if (!resultsToUse || !resultsToUse[index]) return;

    const result = resultsToUse[index];
    const segmentElement = segmentRefs.current[result.segmentId];

    if (segmentElement) {
      segmentElement.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  };

  const handleSearchChange = useCallback(
    (value) => {
      setSearchValue(value);
      performSearch(value);
    },
    [performSearch]
  );

  const handleSearchDialogOpen = useCallback(() => {
    // 打点记录用户点击查找按钮
    if (isAnonymous) {
      trackAnonymousEvent("transcript_search_clicked");
    } else {
      trackEvent("transcript_search_clicked");
    }

    // 如果当前是编辑状态，先退出编辑状态
    if (editingSegmentId !== null) {
      // 找到当前编辑的 segment
      const editingSegment = localSegments?.find(
        (seg) => seg.id === editingSegmentId
      );
      if (editingSegment) {
        // 自动保存当前编辑的内容
        handleSaveEdit(editingSegment);
      } else {
        // 如果找不到段落，直接取消编辑
        handleCancelEdit();
      }
    }

    setShowSearchDialog(true);
    setSearchDialogTab("search");
  }, [
    editingSegmentId,
    localSegments,
    handleSaveEdit,
    handleCancelEdit,
    isAnonymous,
  ]);

  const handleReplaceDialogOpen = useCallback(() => {
    // 打点记录用户点击替换按钮
    if (isAnonymous) {
      trackAnonymousEvent("transcript_replace_clicked");
    } else {
      trackEvent("transcript_replace_clicked");
    }

    // 如果当前是编辑状态，先退出编辑状态
    if (editingSegmentId !== null) {
      // 找到当前编辑的 segment
      const editingSegment = localSegments?.find(
        (seg) => seg.id === editingSegmentId
      );
      if (editingSegment) {
        // 自动保存当前编辑的内容
        handleSaveEdit(editingSegment);
      } else {
        // 如果找不到段落，直接取消编辑
        handleCancelEdit();
      }
    }

    setShowSearchDialog(true);
    setSearchDialogTab("replace");
  }, [
    editingSegmentId,
    localSegments,
    handleSaveEdit,
    handleCancelEdit,
    isAnonymous,
  ]);

  const handleSearchDialogClose = useCallback(() => {
    setShowSearchDialog(false);
    // 清除搜索状态和高亮
    setSearchValue("");
    setSearchResults([]);
    setHighlightedSegments(new Set());
    setCurrentSearchIndex(0);
    setReplaceValue("");
  }, []);

  // 处理复制转录内容到剪贴板
  const handleCopyTranscript = useCallback(async () => {
    // 如果是匿名用户，显示访客弹框
    if (isAnonymous) {
      setGuestDialogSource("transcript_copy");
      setShowGuestModeDialog(true);
      return;
    }

    if (!localSegments || localSegments.length === 0) {
      setToast({
        message: t("common.copyError"),
        variant: "error",
        duration: 3000,
      });
      return;
    }

    setIsCopying(true);

    try {
      // 检查是否有余额不足的情况
      const hasInsufficientMinutes = insufficientMinutes > 0;

      // 根据余额情况决定复制哪些segments
      const segmentsToCopy = hasInsufficientMinutes
        ? localSegments.filter((segment) => isSegmentViewable(segment))
        : localSegments;

      // 拼接可复制segment的text
      const transcriptText = segmentsToCopy
        .map((segment) => segment.text)
        .join(" ");

      let fullText = transcriptText;

      // 如果余额不足，添加提示文案
      if (hasInsufficientMinutes) {
        fullText += `\n\nYou've run out of transcription time. This content has ${insufficientMinutes} more minutes that need to be unlocked. Upgrade to see everything.`;
      }
      // 只有免费用户才添加固定的结尾文字
      else if (!user?.hasPaidPlan) {
        fullText += `\n\nTranscribed by https://www.uniscribe.co`;
      }

      // 复制到剪贴板
      await navigator.clipboard.writeText(fullText);

      // 记录复制事件
      if (isAnonymous) {
        trackAnonymousEvent("transcript_copy_clicked", {
          segments_count: localSegments.length,
          text_length: fullText.length,
        });
      } else {
        trackEvent("transcript_copy_clicked", {
          segments_count: localSegments.length,
          text_length: fullText.length,
        });
      }

      // 显示成功提示
      setToast({
        message: t("common.copySuccess"),
        variant: "success",
        duration: 2000,
      });
    } catch (error) {
      console.error("Error copying transcript:", error);

      // 显示错误提示
      setToast({
        message: t("common.copyError"),
        variant: "error",
        duration: 3000,
      });
    } finally {
      setIsCopying(false);
    }
  }, [localSegments, t, isAnonymous, insufficientMinutes, user]);

  // 处理复制单个segment到剪贴板
  const handleCopySegment = useCallback(
    async (segment) => {
      // 如果是匿名用户，显示访客弹框
      if (isAnonymous) {
        setGuestDialogSource("transcript_copy");
        setShowGuestModeDialog(true);
        return;
      }

      if (!segment || !segment.text) {
        setToast({
          message: t("common.copyError"),
          variant: "error",
          duration: 3000,
        });
        return;
      }

      try {
        // 复制segment的text到剪贴板
        await navigator.clipboard.writeText(segment.text);

        // 记录复制事件
        if (isAnonymous) {
          trackAnonymousEvent("transcript_segment_copy_clicked", {
            segment_id: segment.id,
            text_length: segment.text.length,
          });
        } else {
          trackEvent("transcript_segment_copy_clicked", {
            segment_id: segment.id,
            text_length: segment.text.length,
          });
        }

        // 显示成功提示
        setToast({
          message: t("common.copySuccess"),
          variant: "success",
          duration: 2000,
        });
      } catch (error) {
        console.error("Error copying segment:", error);

        // 显示错误提示
        setToast({
          message: t("common.copyError"),
          variant: "error",
          duration: 3000,
        });
      }
    },
    [t, isAnonymous]
  );

  const handlePreviousSearchResult = useCallback(() => {
    if (currentSearchIndex > 0) {
      const newIndex = currentSearchIndex - 1;
      setCurrentSearchIndex(newIndex);
      // 使用setTimeout确保状态更新后再滚动
      setTimeout(() => {
        scrollToSearchResult(newIndex, searchResults);
      }, 0);
    }
  }, [currentSearchIndex, searchResults]);

  const handleNextSearchResult = useCallback(() => {
    if (currentSearchIndex < searchResults.length - 1) {
      const newIndex = currentSearchIndex + 1;
      setCurrentSearchIndex(newIndex);
      // 使用setTimeout确保状态更新后再滚动
      setTimeout(() => {
        scrollToSearchResult(newIndex, searchResults);
      }, 0);
    }
  }, [currentSearchIndex, searchResults]);

  // 循环跳转到下一个搜索结果（到最后一个后回到第一个）
  const handleNextSearchResultWithLoop = useCallback(() => {
    if (searchResults.length === 0) return;

    const newIndex =
      currentSearchIndex < searchResults.length - 1
        ? currentSearchIndex + 1
        : 0; // 如果是最后一个，回到第一个

    setCurrentSearchIndex(newIndex);
    // 使用setTimeout确保状态更新后再滚动
    setTimeout(() => {
      scrollToSearchResult(newIndex, searchResults);
    }, 0);
  }, [currentSearchIndex, searchResults]);

  // 替换相关函数
  const handleReplaceChange = useCallback((value) => {
    setReplaceValue(value);
  }, []);

  // 单个替换
  const handleReplace = useCallback(async () => {
    if (!searchResults.length || currentSearchIndex >= searchResults.length)
      return;

    const currentResult = searchResults[currentSearchIndex];
    if (!currentResult) return;

    // 设置loading状态
    setIsReplacing(true);

    try {
      // 构建新的文本内容 - 精确替换当前选中的匹配项
      // 使用localSegments中的最新数据，而不是searchResults中的旧数据
      const latestSegment = localSegments.find(
        (seg) => seg.id === currentResult.segmentId
      );
      if (!latestSegment) {
        throw new Error("无法找到要更新的段落");
      }
      const text = latestSegment.text;
      const textLower = text.toLowerCase();
      const searchTermLower = searchValue.toLowerCase();

      // 找到所有匹配项的位置
      const matches = [];
      let startIndex = 0;

      while (true) {
        const foundIndex = textLower.indexOf(searchTermLower, startIndex);
        if (foundIndex === -1) break;

        // 修改为部分匹配：所有找到的匹配项都有效
        const isValidMatch = true;

        if (isValidMatch) {
          matches.push({
            index: foundIndex,
            length: searchTermLower.length,
          });
        }

        startIndex = foundIndex + 1;
      }

      // 根据segmentMatchIndex找到要替换的具体匹配项
      const targetMatch = matches[currentResult.segmentMatchIndex];
      if (!targetMatch) {
        throw new Error("无法找到要替换的匹配项");
      }

      // 精确替换指定位置的文本
      const newText =
        text.slice(0, targetMatch.index) +
        replaceValue +
        text.slice(targetMatch.index + targetMatch.length);

      // 调用新的批量更新API
      const response = await transcriptionService.batchUpdateSegments(fileId, [
        { id: latestSegment.id, text: newText },
      ]);

      if (response.data && response.data.success) {
        // 更新本地segments副本
        // 新的批量接口返回的是segments数组，取第一个元素
        const updatedSegment = response.data.segments[0];
        setLocalSegments((prevSegments) =>
          prevSegments.map((seg) =>
            seg.id === latestSegment.id ? updatedSegment : seg
          )
        );

        // 替换成功后重新执行搜索，确保搜索结果基于最新的文本内容
        // 使用更新后的localSegments重新搜索
        setTimeout(() => {
          setLocalSegments((currentSegments) => {
            // 基于最新的segments数据重新执行搜索
            const results = [];
            const highlightedSegmentIds = new Set();

            if (searchValue.trim()) {
              currentSegments.forEach((segment, segmentIndex) => {
                const text = segment.text.toLowerCase();
                const searchTermLower = searchValue.toLowerCase();

                let startIndex = 0;
                let segmentMatchIndex = 0;

                while (true) {
                  const foundIndex = text.indexOf(searchTermLower, startIndex);
                  if (foundIndex === -1) break;

                  // 修改为部分匹配：所有找到的匹配项都有效
                  const isValidMatch = true;

                  if (isValidMatch) {
                    const originalMatch = segment.text.slice(
                      foundIndex,
                      foundIndex + searchTermLower.length
                    );

                    results.push({
                      segmentId: segment.id,
                      segmentIndex: segmentIndex,
                      matchIndex: foundIndex,
                      matchText: originalMatch,
                      segment: segment,
                      uniqueId: `${segment.id}-${segmentMatchIndex}`,
                      segmentMatchIndex: segmentMatchIndex,
                    });
                    highlightedSegmentIds.add(segment.id);
                    segmentMatchIndex++;
                  }

                  startIndex = foundIndex + 1;
                }
              });
            }

            setSearchResults(results);
            setHighlightedSegments(highlightedSegmentIds);
            setCurrentSearchIndex(0);

            if (results.length > 0) {
              setTimeout(() => scrollToSearchResult(0, results), 50);
            }

            return currentSegments; // 返回不变的segments
          });
        }, 100);

        // 记录替换成功事件
        if (isAnonymous) {
          trackAnonymousEvent("transcript_replace_success", {
            search_term: searchValue,
            replace_term: replaceValue,
          });
        } else {
          trackEvent("transcript_replace_success", {
            search_term: searchValue,
            replace_term: replaceValue,
          });
        }

        // 显示成功提示
        setToast({
          message: t("transcript.replaceSuccess"),
          variant: "success",
          duration: 2000,
        });

        // 注意：这里不调用 onSegmentClick，避免设置segment为选中状态
        // 本地数据已更新，页面会实时显示新的文本
      }
    } catch (error) {
      console.error("Error replacing text:", error);

      // 记录替换失败事件
      if (isAnonymous) {
        trackAnonymousEvent("transcript_replace_error", {
          search_term: searchValue,
          replace_term: replaceValue,
          error: error.message || "Unknown error",
        });
      } else {
        trackEvent("transcript_replace_error", {
          search_term: searchValue,
          replace_term: replaceValue,
          error: error.message || "Unknown error",
        });
      }

      setToast({
        message: t("transcript.replaceError"),
        variant: "error",
        duration: 3000,
      });
    } finally {
      // 清除loading状态
      setIsReplacing(false);
    }
  }, [
    searchResults,
    currentSearchIndex,
    searchValue,
    replaceValue,
    fileId,
    t,
    isAnonymous,
  ]);

  // 全部替换
  const handleReplaceAll = useCallback(async () => {
    if (!searchResults.length) return;

    // 设置loading状态
    setIsReplacingAll(true);

    try {
      // 构建替换数据 - 按segment分组并替换所有匹配项
      const segmentReplacements = new Map();

      // 按segment分组搜索结果
      searchResults.forEach((result) => {
        if (!segmentReplacements.has(result.segmentId)) {
          segmentReplacements.set(result.segmentId, {
            segment: result.segment,
            matches: [],
          });
        }
        segmentReplacements.get(result.segmentId).matches.push(result);
      });

      // 对每个segment处理所有匹配项
      const replacements = Array.from(segmentReplacements.values()).map(
        ({ segment }) => {
          // 使用localSegments中的最新数据，而不是searchResults中的旧数据
          const latestSegment = localSegments.find(
            (seg) => seg.id === segment.id
          );
          if (!latestSegment) {
            throw new Error(`无法找到要更新的段落: ${segment.id}`);
          }

          let newText = latestSegment.text;
          const textLower = newText.toLowerCase();
          const searchTermLower = searchValue.toLowerCase();

          // 找到所有匹配位置（从后往前替换，避免位置偏移）
          const allMatches = [];
          let startIndex = 0;

          while (true) {
            const foundIndex = textLower.indexOf(searchTermLower, startIndex);
            if (foundIndex === -1) break;

            // 修改为部分匹配：所有找到的匹配项都有效
            const isValidMatch = true;

            if (isValidMatch) {
              allMatches.push({
                index: foundIndex,
                length: searchTermLower.length,
              });
            }

            startIndex = foundIndex + 1;
          }

          // 从后往前替换，避免位置偏移
          allMatches.reverse().forEach((match) => {
            newText =
              newText.slice(0, match.index) +
              replaceValue +
              newText.slice(match.index + match.length);
          });

          return {
            id: segment.id,
            text: newText,
          };
        }
      );

      // 调用新的批量更新API
      const response = await transcriptionService.batchUpdateSegments(
        fileId,
        replacements
      );

      if (response.data && response.data.success) {
        // 更新本地segments数据
        const updatedSegments = response.data.segments;
        setLocalSegments((prevSegments) => {
          const segmentMap = new Map(
            updatedSegments.map((seg) => [seg.id, seg])
          );
          return prevSegments.map((seg) => segmentMap.get(seg.id) || seg);
        });

        // 清除所有搜索结果和高亮
        setSearchResults([]);
        setHighlightedSegments(new Set());
        setCurrentSearchIndex(0);

        // 记录全部替换成功事件
        if (isAnonymous) {
          trackAnonymousEvent("transcript_replace_all_success", {
            search_term: searchValue,
            replace_term: replaceValue,
            segments_count: replacements.length,
          });
        } else {
          trackEvent("transcript_replace_all_success", {
            search_term: searchValue,
            replace_term: replaceValue,
            segments_count: replacements.length,
          });
        }

        // 显示成功提示
        setToast({
          message: t("transcript.replaceAllSuccess"),
          variant: "success",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("Error replacing all text:", error);

      // 记录全部替换失败事件
      if (isAnonymous) {
        trackAnonymousEvent("transcript_replace_all_error", {
          search_term: searchValue,
          replace_term: replaceValue,
          error: error.message || "Unknown error",
        });
      } else {
        trackEvent("transcript_replace_all_error", {
          search_term: searchValue,
          replace_term: replaceValue,
          error: error.message || "Unknown error",
        });
      }

      setToast({
        message: error.message || t("transcript.replaceAllError"),
        variant: "error",
        duration: 3000,
      });
    } finally {
      // 清除loading状态
      setIsReplacingAll(false);
    }
  }, [
    searchResults,
    searchValue,
    replaceValue,
    fileId,
    t,
    isAnonymous,
    localSegments,
  ]);

  // 高亮文本的函数（搜索高亮）
  const highlightText = useCallback(
    (text, segmentId) => {
      if (!searchValue.trim() || !highlightedSegments.has(segmentId)) {
        return text;
      }

      // 获取当前应该高亮的匹配项
      const currentResult = searchResults[currentSearchIndex];

      const textLower = text.toLowerCase();
      const searchTermLower = searchValue.toLowerCase();

      let lastIndex = 0;
      const result = [];
      let startIndex = 0;
      let segmentMatchIndex = 0;

      while (true) {
        // 在文本中查找关键词
        const foundIndex = textLower.indexOf(searchTermLower, startIndex);
        if (foundIndex === -1) break;

        // 修改为部分匹配：所有找到的匹配项都有效
        const isValidMatch = true;

        if (isValidMatch) {
          // 添加匹配前的文本
          if (foundIndex > lastIndex) {
            result.push(text.slice(lastIndex, foundIndex));
          }

          // 判断是否是当前高亮的匹配项
          const isCurrentMatch =
            currentResult &&
            currentResult.segmentId === segmentId &&
            currentResult.segmentMatchIndex === segmentMatchIndex;

          const highlightClass = isCurrentMatch
            ? "bg-yellow-300" // 当前匹配项用黄色
            : "bg-custom-bg text-white"; // 其他匹配项用主题色

          // 获取原始文本中的匹配部分（保持原始大小写）
          const originalMatch = text.slice(
            foundIndex,
            foundIndex + searchTermLower.length
          );

          result.push(
            <span
              key={`${segmentId}-${segmentMatchIndex}`}
              className={highlightClass}
            >
              {originalMatch}
            </span>
          );

          lastIndex = foundIndex + searchTermLower.length;
          segmentMatchIndex++;
        }

        // 移动到下一个位置继续搜索
        startIndex = foundIndex + 1;
      }

      // 添加最后的文本
      if (lastIndex < text.length) {
        result.push(text.slice(lastIndex));
      }

      return result;
    },
    [searchValue, highlightedSegments, searchResults, currentSearchIndex]
  );

  // 获取当前应该显示的任务状态（仅处理转录和预处理）
  const getCurrentTaskStatus = useCallback(() => {
    // 如果当前文件状态是预处理中
    if (
      fileStatus === FILE_STATUS.PREPROCESSING ||
      fileStatus === FILE_STATUS.PREPROCESSING_FAILED
    ) {
      return taskStatuses?.mediaPreprocessing;
    }

    // 默认显示转录状态
    return taskStatuses?.transcription;
  }, [fileStatus, taskStatuses]);

  // 获取当前应该显示的错误信息（仅处理转录和预处理）
  const getCurrentTaskError = useCallback(() => {
    // 如果文件状态是预处理失败
    if (fileStatus === FILE_STATUS.PREPROCESSING_FAILED) {
      return taskErrors?.mediaPreprocessing;
    }

    // 默认显示转录错误
    return taskErrors?.transcription;
  }, [fileStatus, taskErrors]);

  // 获取当前任务的ID（仅处理转录和预处理）
  const getCurrentTaskId = useCallback(() => {
    // 如果文件状态是预处理失败
    if (fileStatus === FILE_STATUS.PREPROCESSING_FAILED) {
      return taskIds?.mediaPreprocessing;
    }

    // 默认返回转录任务ID
    return taskIds?.transcription;
  }, [fileStatus, taskIds]);

  // 获取当前任务类型（仅处理转录和预处理）
  const getCurrentTaskType = useCallback(() => {
    // 如果文件状态是预处理失败
    if (fileStatus === FILE_STATUS.PREPROCESSING_FAILED) {
      return "mediaPreprocessing";
    }

    // 默认返回转录任务类型
    return "transcription";
  }, [fileStatus]);

  // 检查当前任务是否正在重试
  const getCurrentTaskRetrying = useCallback(() => {
    const taskId = getCurrentTaskId();
    return taskId ? isTaskRetrying(taskId) : false;
  }, [getCurrentTaskId, isTaskRetrying]);

  // 获取当前任务的重试信息
  const getCurrentTaskRetryInfo = useCallback(() => {
    const taskId = getCurrentTaskId();
    return taskId ? getTaskRetryInfo(taskId) : null;
  }, [getCurrentTaskId, getTaskRetryInfo]);

  // 获取当前任务的显示名称（仅处理转录和预处理）
  const getCurrentTaskName = useCallback(() => {
    const taskType = getCurrentTaskType();
    switch (taskType) {
      case "mediaPreprocessing":
        return t("transcript.mediaPreprocessing");
      case "transcription":
      default:
        return t("transcript.title");
    }
  }, [getCurrentTaskType, t]);

  // 生成说话人识别处理中的消息
  const getSpeakerDiarizationMessage = useCallback(() => {
    const status = taskStatuses?.speakerDiarization;
    const shouldShowProcessing =
      status === "processing" || (status === "completed" && !isAligned);

    if (!shouldShowProcessing) {
      return null;
    }

    return (
      <div className="p-3 rounded-lg bg-custom-bg-50 border border-custom-bg-200">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-custom-bg-800">
            {t("transcript.status.speaker_diarization_processing")}
          </span>
        </div>
        <p className="text-xs text-custom-bg-600 mt-1">
          {t("transcript.status.speaker_diarization_processing_hint")}
        </p>
      </div>
    );
  }, [taskStatuses?.speakerDiarization, isAligned, t]);

  // 检查是否应该显示说话人识别状态
  const shouldShowSpeakerDiarizationStatus = useCallback(() => {
    const status = taskStatuses?.speakerDiarization;
    return (
      status === "processing" ||
      status === "failed" ||
      (status === "completed" && !isAligned)
    );
  }, [taskStatuses?.speakerDiarization, isAligned]);

  // 获取说话人识别任务的重试状态和信息
  const getSpeakerDiarizationRetryProps = useCallback(() => {
    const taskId = taskIds?.speakerDiarization;
    return {
      isRetrying: taskId ? isTaskRetrying(taskId) : false,
      retryInfo: taskId ? getTaskRetryInfo(taskId) : null,
    };
  }, [taskIds?.speakerDiarization, isTaskRetrying, getTaskRetryInfo]);

  const getProcessingMessage = () => {
    // Check if this is preprocessing phase
    if (fileStatus === FILE_STATUS.PREPROCESSING) {
      // If it's YouTube source, show YouTube downloading message
      if (sourceType === "youtube") {
        return (
          <span className="text-custom-bg font-medium">
            {t("transcript.status.youtube_downloading", {
              defaultValue: "Downloading YouTube media file...",
            })}
          </span>
        );
      }

      // Otherwise show media preprocessing message
      // Check if file is large (over 200MB)
      const isLargeFile = fileSize && fileSize > 200 * 1024 * 1024; // 200MB in bytes

      if (isLargeFile) {
        return (
          <div className="text-custom-bg font-medium">
            <div className="mb-2">
              {t("transcript.status.media_preprocessing_large", {
                defaultValue:
                  "Preprocessing your large file - this may take a while...",
              })}
            </div>
            <div className="text-sm text-custom-bg-600 font-normal">
              {t("transcript.status.can_leave_page", {
                defaultValue:
                  "You can leave this page or even close your browser. We'll continue processing in the background.",
              })}
            </div>
          </div>
        );
      }

      return (
        <span className="text-custom-bg font-medium">
          {t("transcript.status.media_preprocessing", {
            defaultValue: "Preprocessing your file...",
          })}
        </span>
      );
    }

    // Default processing messages
    if (user?.hasPaidPlan) {
      return (
        <span className="text-custom-bg font-medium">
          {t("transcript.status.premium_transcribing")}
        </span>
      );
    }
    return t("transcript.status.premium_transcribing_free_trial");
  };

  return (
    <TooltipProvider delayDuration={300}>
      <div className="h-full flex flex-col bg-gray-50">
        {/* 显示Toast提示 */}
        {toast && (
          <ToastContainer
            variant={toast.variant}
            position="top"
            className="z-50 flex items-center justify-between gap-2 px-4 py-2"
          >
            <div className="flex items-center gap-2">
              {toast.variant === "success" && (
                <CheckCircle className="h-4 w-4 text-green-600" />
              )}
              {toast.variant === "error" && (
                <X className="h-4 w-4 text-red-600" />
              )}
              <span>{toast.message}</span>
            </div>
            <button
              onClick={() => setToast(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4" />
            </button>
          </ToastContainer>
        )}

        <TaskStatusHandler
          status={getCurrentTaskStatus()}
          error={getCurrentTaskError()}
          taskId={getCurrentTaskId()}
          taskType={getCurrentTaskType()}
          onRetry={onRetry}
          isRetrying={getCurrentTaskRetrying()}
          retryInfo={getCurrentTaskRetryInfo()}
          taskName={getCurrentTaskName()}
          message={getProcessingMessage()}
        >
          <div className="relative flex-1 min-h-0">
            <div ref={containerRef} className="h-full overflow-y-auto">
              {/* Sticky 头部 - 移到滚动容器内部 */}
              <div className="flex-none px-6 pt-4 pb-2 bg-gray-50 sticky top-0 z-10">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-bold">{t("transcript.title")}</h2>

                  {/* 操作区域 - 移动端不显示 */}
                  {!isMobile && (
                    <div className="flex items-center space-x-2">
                      {/* 查找按钮 - 匿名用户也可以使用 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-600 hover:text-custom-bg hover:bg-custom-bg/10"
                            onClick={handleSearchDialogOpen}
                          >
                            <Search className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("transcript.search")}</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* 替换按钮 - 匿名用户也可以使用 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-600 hover:text-custom-bg hover:bg-custom-bg/10"
                            onClick={handleReplaceDialogOpen}
                          >
                            <Replace className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("transcript.replace")}</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* 复制按钮 - 匿名用户也可以使用 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-600 hover:text-custom-bg hover:bg-custom-bg/10"
                            onClick={handleCopyTranscript}
                            disabled={isCopying}
                          >
                            {isCopying ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="left" align="center">
                          <p>{t("common.copyTranscript")}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  )}
                </div>
              </div>

              {/* 转录内容区域 */}
              <div className="px-6 pb-6">
                {/* 说话人识别任务状态处理器 */}
                {shouldShowSpeakerDiarizationStatus() && (
                  <TaskStatusHandler
                    status={taskStatuses?.speakerDiarization}
                    error={taskErrors?.speakerDiarization}
                    taskId={taskIds?.speakerDiarization}
                    taskType="speakerDiarization"
                    onRetry={onRetry}
                    {...getSpeakerDiarizationRetryProps()}
                    taskName={t("transcript.speakerDiarization")}
                    message={getSpeakerDiarizationMessage()}
                  />
                )}

                {localSegments?.map((segment) => {
                  const isCurrentSegment = currentSegment?.id === segment.id;

                  if (!isSegmentViewable(segment)) {
                    return null;
                  }

                  return (
                    <div
                      key={segment.id}
                      ref={(el) => (segmentRefs.current[segment.id] = el)}
                      className="
                    mb-2
                    cursor-pointer
                    relative
                    rounded-lg
                  "
                      onClick={() => handleSegmentClick(segment)}
                      onMouseEnter={() => setHoveredSegmentId(segment.id)}
                      onMouseLeave={() => setHoveredSegmentId(null)}
                    >
                      {/* 操作按钮 - 悬浮时显示在右上角，编辑状态下不显示 */}
                      {(hoveredSegmentId === segment.id || isCurrentSegment) &&
                        editingSegmentId !== segment.id &&
                        !isMobile && (
                          <div className="absolute top-2 right-2 flex items-center space-x-2 z-10">
                            {/* 播放按钮 - 只在 canPlay 为 true 时显示 */}
                            {canPlay && (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 text-gray-600 hover:text-custom-bg hover:bg-custom-bg/10"
                                    onClick={(e) => handlePlayClick(e, segment)}
                                  >
                                    {isPlaying &&
                                    currentSegment?.id === segment.id ? (
                                      <Pause className="h-4 w-4" />
                                    ) : (
                                      <Play className="h-4 w-4" />
                                    )}
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    {isPlaying &&
                                    currentSegment?.id === segment.id
                                      ? t("transcript.pause")
                                      : t("transcript.play")}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            )}

                            {/* 复制按钮 */}
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-gray-600 hover:text-custom-bg hover:bg-custom-bg/10"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleCopySegment(segment);
                                  }}
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{t("common.copy")}</p>
                              </TooltipContent>
                            </Tooltip>

                            {/* 编辑按钮 */}
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-gray-600 hover:text-custom-bg hover:bg-custom-bg/10"
                                  onClick={(e) => handleEditClick(e, segment)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent
                                side="left"
                                align="center"
                                avoidCollisions={true}
                                className="max-w-xs text-sm"
                              >
                                <p>{t("transcript.edit")}</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        )}

                      <div className="p-4 rounded-lg">
                        {/* 发言人和时间信息 */}
                        <div className="flex items-center space-x-2 mb-3">
                          <SpeakerAvatar
                            speakerName={segment.speaker || "Speaker"}
                            size="md"
                            variant="colored"
                          />

                          {/* Speaker 名称区域 - 支持编辑 */}
                          {editingSpeakerId === segment.id ? (
                            <div
                              className="flex items-center space-x-2"
                              data-speaker-edit-container
                            >
                              <input
                                type="text"
                                value={editedSpeakerName}
                                onChange={handleSpeakerNameChange}
                                className="h-8 px-3 text-sm font-medium text-gray-700 bg-white border border-custom-bg rounded-md focus:outline-none focus:ring-1 focus:ring-custom-bg min-w-[80px] max-w-[120px] w-full"
                                onClick={(e) => e.stopPropagation()}
                                onKeyDown={(e) => {
                                  if (e.key === "Escape") {
                                    handleSpeakerEditCancel();
                                  } else if (e.key === "Enter") {
                                    e.preventDefault();
                                    handleSpeakerSaveSingle(segment);
                                  }
                                }}
                                autoFocus
                              />
                              <div className="flex items-center space-x-2">
                                <Button
                                  variant="default"
                                  size="sm"
                                  className="h-8 px-4 text-sm bg-custom-bg hover:bg-custom-bg/90 text-white border-0"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSpeakerSaveGlobal(segment);
                                  }}
                                  disabled={isSavingSpeaker}
                                >
                                  {isSavingSpeaker ? (
                                    <>
                                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                      {t("transcript.speakerEditAll")}
                                    </>
                                  ) : (
                                    t("transcript.speakerEditAll")
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 px-4 text-sm text-gray-600 hover:text-custom-bg hover:border-custom-bg border-gray-300"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSpeakerSaveSingle(segment);
                                  }}
                                  disabled={isSavingSpeaker}
                                >
                                  {isSavingSpeaker ? (
                                    <>
                                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                      {t("transcript.speakerEditSingle")}
                                    </>
                                  ) : (
                                    t("transcript.speakerEditSingle")
                                  )}
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div
                              className={`flex items-center space-x-1 group ${
                                isSpeakerDiarizationProcessing()
                                  ? "cursor-not-allowed opacity-60"
                                  : "cursor-pointer"
                              }`}
                              onMouseEnter={() =>
                                !isSpeakerDiarizationProcessing() &&
                                setHoveredSpeakerId(segment.id)
                              }
                              onMouseLeave={() => setHoveredSpeakerId(null)}
                              onClick={(e) => {
                                e.stopPropagation();
                                if (!isSpeakerDiarizationProcessing()) {
                                  handleSpeakerEditClick(e, segment);
                                }
                              }}
                            >
                              {isSpeakerDiarizationProcessing() && (
                                <Loader2 className="h-3 w-3 animate-spin text-custom-bg mr-1" />
                              )}
                              <span className="text-sm font-medium text-gray-500 group-hover:text-custom-bg transition-colors">
                                {segment.speaker || "Speaker"}
                              </span>
                              {!isMobile &&
                                !isSpeakerDiarizationProcessing() &&
                                (hoveredSpeakerId === segment.id ||
                                  hoveredSegmentId === segment.id) && (
                                  <Edit className="h-3 w-3 text-gray-400 group-hover:text-custom-bg transition-colors" />
                                )}
                            </div>
                          )}

                          <span className="text-sm text-gray-400">
                            {formatDuration(segment.start)} -{" "}
                            {formatDuration(segment.end)}
                          </span>

                          {/* 编辑状态提示 - 放在时间信息右侧 */}
                          {editingSegmentId === segment.id && (
                            <div className="ml-2 text-xs px-2 py-1 rounded-full bg-custom-bg/10 text-custom-bg flex items-center space-x-1">
                              <Edit className="h-3 w-3" />
                              <span>{t("transcript.editingTip")}</span>
                            </div>
                          )}
                        </div>

                        {/* 文本内容 - 编辑模式或显示模式 */}
                        {editingSegmentId === segment.id ? (
                          <div className="relative">
                            <textarea
                              ref={(el) =>
                                (textareaRefs.current[segment.id] = el)
                              }
                              value={editedText}
                              onChange={handleTextChange}
                              className={`w-full p-3 rounded-lg focus:outline-none focus:ring-0 text-sm leading-relaxed min-h-[1.5rem] bg-white text-gray-600 border-2 border-custom-bg`}
                              onClick={(e) => e.stopPropagation()}
                              onKeyDown={(e) => {
                                // 处理 Ctrl+Enter 或 Cmd+Enter 保存
                                if (
                                  (e.ctrlKey || e.metaKey) &&
                                  e.key === "Enter"
                                ) {
                                  e.preventDefault();
                                  handleSaveEdit(segment);
                                }
                              }}
                              onBlur={(e) => {
                                // 防止事件冒泡，确保不会触发其他点击事件
                                e.stopPropagation();

                                // 注意：我们不在这里自动保存，因为现在有明确的保存按钮
                                // 用户可能会点击取消按钮，所以我们不应该在失去焦点时自动保存
                              }}
                              style={{
                                resize: "none",
                                overflow: "hidden", // 隐藏滚动条
                                lineHeight: "inherit",
                              }}
                            />

                            {/* 保存和取消按钮 */}
                            <div className="flex justify-end mt-2 space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-gray-500 hover:text-gray-700"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCancelEdit();
                                }}
                              >
                                {t("transcript.cancel")}
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                className="bg-custom-bg hover:bg-custom-bg/90 text-white"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSaveEdit(segment);
                                }}
                                disabled={isSaving}
                              >
                                {isSaving ? (
                                  <>
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    {t("transcript.save")}
                                  </>
                                ) : (
                                  t("transcript.save")
                                )}
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div
                            className={`p-3 rounded-lg ${
                              isCurrentSegment
                                ? "bg-custom-bg text-white border-2 border-custom-bg"
                                : "bg-white text-gray-600 border-2 border-transparent hover:border-custom-bg"
                            }`}
                            onClick={(e) => {
                              // 先阻止事件冒泡，避免触发外层容器的点击事件
                              e.stopPropagation();

                              // 如果不是当前选中的段落，先选中它并跳转到对应时间点
                              if (!isCurrentSegment) {
                                handleSegmentClick(segment, false); // 不跳过 seek，允许进度条跳转
                              }

                              // 单击只选择段落，不进入编辑模式
                            }}
                            onDoubleClick={(e) => {
                              if (isMobile) return; // 移动端禁用双击编辑
                              // 阻止事件冒泡
                              e.stopPropagation();

                              // 双击时进入编辑模式，传递 'double_click' 作为来源
                              handleEditClick(e, segment, "double_click");
                            }}
                          >
                            <p className="text-sm leading-relaxed min-h-[1.5rem]">
                              {highlightText(segment.text, segment.id)}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {shouldShowOverlay && overlayProps && (
                <div className="absolute bottom-0 left-0 right-0 h-48 bg-gradient-to-t from-white via-white to-transparent pointer-events-none">
                  <div className="absolute bottom-0 left-0 right-0 flex flex-col items-center justify-center p-6 pointer-events-auto">
                    <p className="text-sm text-custom-bg text-center mb-4">
                      {overlayProps.message}
                    </p>
                    <Button
                      className="bg-custom-bg hover:bg-custom-bg/90"
                      onClick={overlayProps.buttonAction}
                    >
                      {overlayProps.buttonText}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TaskStatusHandler>

        {/* 搜索替换弹框 */}
        <SearchReplaceDialog
          isOpen={showSearchDialog}
          onClose={handleSearchDialogClose}
          activeTab={searchDialogTab}
          onTabChange={setSearchDialogTab}
          searchValue={searchValue}
          onSearchChange={handleSearchChange}
          searchResults={searchResults}
          currentSearchIndex={currentSearchIndex}
          onPreviousResult={handlePreviousSearchResult}
          onNextResult={handleNextSearchResult}
          onNextResultWithLoop={handleNextSearchResultWithLoop}
          replaceValue={replaceValue}
          onReplaceChange={handleReplaceChange}
          onReplace={handleReplace}
          onReplaceAll={handleReplaceAll}
          isReplacing={isReplacing}
          isReplacingAll={isReplacingAll}
        />

        {/* 访客引导登录弹框 */}
        <GuestModeDialog
          isOpen={showGuestModeDialog}
          onClose={() => setShowGuestModeDialog(false)}
          source={guestDialogSource}
        />
      </div>
    </TooltipProvider>
  );
}
