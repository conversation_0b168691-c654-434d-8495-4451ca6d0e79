import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useTranslations } from "next-intl";
import { Loader2, AlertTriangle } from "lucide-react";

const DeleteDialog = ({
  isOpen,
  onOpenChange,
  filename,
  onDelete,
  // 批量删除相关参数
  isBatch = false,
  selectedCount = 0,
  isLoading = false,
}) => {
  const t = useTranslations(
    isBatch ? "dashboard.batchDelete" : "dashboard.deleteDialog"
  );

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle
            className={isBatch ? "flex items-center gap-2" : ""}
          >
            {isBatch && <AlertTriangle className="h-5 w-5 text-destructive" />}
            {t("title")}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {isBatch ? (
              <div className="space-y-3">
                <p>{t("description", { count: selectedCount })}</p>
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{t("warning")}</AlertDescription>
                </Alert>
              </div>
            ) : (
              t("confirm", { filename })
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {t("cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelete}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isBatch ? t("deleting") : t("delete")}
              </>
            ) : isBatch ? (
              t("confirm")
            ) : (
              t("delete")
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteDialog;
