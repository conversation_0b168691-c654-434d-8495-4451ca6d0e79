"use client";

import React from "react";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  AlertCircle,
  ArrowUpRight,
  Shield,
  Clock,
  X,
  Check,
} from "lucide-react";
import { trackAnonymousEvent } from "@/lib/analytics";
import { ANONYMOUS_USER_LIMITS } from "@/constants/file";

/**
 * 访客引导登录弹框
 * @param {boolean} isOpen - 是否显示弹框
 * @param {Function} onClose - 关闭弹框的回调函数
 * @param {string} source - 弹框来源，用于埋点和确定显示的文案
 * @param {number} remainingMinutes - 剩余转录分钟数（仅用于minutes_limited场景）
 * @param {number} requiredMinutes - 当前转录需要的分钟数（仅用于minutes_limited场景）
 */
const GuestModeDialog = ({
  isOpen,
  onClose,
  source = "back_to_home_dialog",
  remainingMinutes = 0,
  requiredMinutes = 0,
}) => {
  const t = useTranslations("common.guestModeDialog");
  const router = useRouter();

  // 根据source获取对应的描述文案
  const getSourceMessage = (source) => {
    // 如果source为空或无效，使用默认文案
    if (!source || typeof source !== "string" || source.trim() === "") {
      return t("messages.default");
    }

    const messageKey = `messages.${source}`;
    // 检查是否存在对应的翻译，如果不存在则使用默认文案
    try {
      // 对于minutes_limited，传递分钟数参数
      if (source === "minutes_limited") {
        const totalLimit = ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES; // 总限制分钟数
        const usedMinutes = totalLimit - remainingMinutes;

        return t(messageKey, {
          remainingMinutes,
          totalLimit,
          usedMinutes,
          requiredMinutes,
        });
      }

      return t(messageKey);
    } catch (error) {
      return t("messages.default");
    }
  };

  const handleSignUpFree = () => {
    // 跟踪事件
    trackAnonymousEvent("signin_click", {
      source,
    });

    // 关闭弹框
    onClose();

    // 跳转到注册页
    router.push("/auth/signup");
  };

  const handleSignInExisting = () => {
    // 跟踪事件
    trackAnonymousEvent("signin_click", {
      source,
    });

    // 关闭弹框
    onClose();

    // 跳转到登录页
    router.push("/auth/signin");
  };

  const handleDialogClose = () => {
    // 跟踪关闭事件
    trackAnonymousEvent("dialog_close_click", {
      source,
    });

    // 关闭弹框
    onClose();

    // 如果是从返回按钮触发的弹框，点击关闭按钮跳转到首页
    if (source === "back_to_home_dialog") {
      router.push("/");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogContent className="max-w-2xl p-0">
        {/* 顶部区域 - 营销主题色背景 */}
        <div className="bg-marketing-50 border-b border-marketing-100 p-6 text-center">
          <div className="w-12 h-12 bg-marketing-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <AlertCircle className="w-6 h-6 text-marketing-600" />
          </div>
          <h2 className="text-xl font-semibold text-marketing-900 mb-2">
            {t("featureLimited")}
          </h2>
          <p className="text-marketing-700">{getSourceMessage(source)}</p>
          <div className="mt-3">
            <div className="flex items-center justify-center gap-2">
              <Check className="w-4 h-4 text-marketing-900" />
              <span className="text-sm text-marketing-900 font-medium">
                {t("contentWontBeLost")}
              </span>
            </div>
          </div>
        </div>

        {/* 中部区域 */}
        <div className="p-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* 访客状态列 */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-700">
                  {t("guestAccess")}
                </h3>
                <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">
                  {t("youAreHere")}
                </span>
              </div>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">{t("transcription")}</span>
                  <span className="text-gray-900">
                    {t("guestMinutes", {
                      minutes: ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES,
                    })}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t("exports")}</span>
                  <X className="w-4 h-4 text-marketing-600" />
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t("history")}</span>
                  <X className="w-4 h-4 text-marketing-600" />
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t("dailyLimit")}</span>
                  <span className="text-gray-900">{t("oneTotal")}</span>
                </div>
              </div>
            </div>

            {/* 免费账户列 */}
            <div className="bg-marketing-50 border-2 border-marketing-200 rounded-lg p-5">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-marketing-800">
                  {t("freeAccount")}
                </h3>
                <div className="flex items-center gap-1">
                  <span className="text-xs bg-marketing-600 text-white px-2 py-1 rounded">
                    {t("afterSignUp")}
                  </span>
                  <ArrowUpRight className="w-4 h-4 text-marketing-600" />
                </div>
              </div>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">{t("transcription")}</span>
                  <span className="text-marketing-700 font-medium">
                    {t("oneHundredTwentyMinPerMonth")}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t("exports")}</span>
                  <div className="flex items-center gap-1 text-marketing-700 font-medium">
                    <Check className="w-4 h-4" />
                    <span>{t("sixFormats")}</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t("history")}</span>
                  <div className="flex items-center gap-1 text-marketing-700 font-medium">
                    <Check className="w-4 h-4" />
                    <span>{t("thirtyDays")}</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t("dailyLimit")}</span>
                  <span className="text-marketing-700 font-medium">
                    {t("threePerDay")}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 主要操作按钮 */}
          <div className="mt-6">
            <Button
              onClick={handleSignUpFree}
              className="w-full bg-marketing-600 hover:bg-marketing-700 text-white"
            >
              <ArrowUpRight className="w-4 h-4 mr-2" />
              {t("signUpFree")}
            </Button>
          </div>

          {/* 信任文案 */}
          <div className="text-center mt-3">
            <div className="flex items-center justify-center gap-4 text-xs text-gray-600">
              <div className="flex items-center gap-1">
                <Shield className="w-3 h-3 text-green-600" />
                <span>{t("noCreditCardRequired")}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3 text-marketing-600" />
                <span>{t("signUpTakesThirtySeconds")}</span>
              </div>
            </div>
          </div>

          {/* 底部登录引导 */}
          <div className="text-center mt-4">
            <button
              onClick={handleSignInExisting}
              className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
            >
              {t("haveAnAccount")}{" "}
              <span className="underline">{t("signInLink")}</span>
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GuestModeDialog;
