"use client";

import React from "react";
import * as Sentry from "@sentry/nextjs";

class TestErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误到 Sentry（模拟全局错误边界的行为）
    console.error("TestErrorBoundary caught an error:", error, errorInfo);

    if (typeof window !== "undefined") {
      Sentry.withScope((scope) => {
        scope.setTag("errorBoundary", "test");
        scope.setContext("errorDetails", {
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          userAgent: window.navigator.userAgent,
          url: window.location.href,
        });
        scope.setLevel("error");
        Sentry.captureException(error);
      });
    }
  }

  handleRetry = () => {
    // 重置错误状态
    this.setState({ hasError: false, error: null });

    // 记录重试操作
    Sentry.addBreadcrumb({
      message: "User attempted to retry after test error",
      level: "info",
      category: "user-action",
    });
  };

  handleGoHome = () => {
    // 记录导航操作
    Sentry.addBreadcrumb({
      message: "User navigated to homepage after test error",
      level: "info",
      category: "user-action",
    });

    window.location.href = "/";
  };

  render() {
    if (this.state.hasError) {
      // 渲染自定义的错误 UI（模拟全局错误页面）
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
          <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
            <div className="text-center">
              <div className="text-red-500 text-6xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold text-red-600 mb-4">
                Oops! Something went wrong
              </h1>
              <p className="text-gray-600 mb-2">
                We&apos;re sorry, but we&apos;ve encountered an unexpected
                error.
              </p>
              <p className="text-gray-600 mb-6">
                Our team has been automatically notified and we&apos;re working
                to fix the issue.
              </p>

              <div className="flex gap-3 justify-center flex-wrap">
                <button
                  onClick={this.handleRetry}
                  className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                >
                  Try Again
                </button>
                <button
                  onClick={this.handleGoHome}
                  className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                >
                  Return to Homepage
                </button>
              </div>

              {/* 开发环境显示错误详情 */}
              {process.env.NODE_ENV === "development" && this.state.error && (
                <details className="mt-6 text-left bg-gray-100 p-4 rounded border">
                  <summary className="cursor-pointer font-bold mb-2">
                    Error Details (Development Only)
                  </summary>
                  <pre className="text-xs overflow-auto max-h-40 whitespace-pre-wrap">
                    <strong>Message:</strong> {this.state.error.message}
                    {this.state.error.stack && (
                      <>
                        {"\n"}
                        <strong>Stack:</strong>
                        {"\n"}
                        {this.state.error.stack}
                      </>
                    )}
                  </pre>
                </details>
              )}

              <div className="mt-4 text-xs text-gray-500">
                🧪 This is a test error boundary (not the global one)
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default TestErrorBoundary;
